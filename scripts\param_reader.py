#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy

def get_nav_and_operator():
    """
    从ROS参数服务器获取NAV_END和OPERATOR参数
    """
    rospy.init_node('param_reader', anonymous=True)
    
    print("=" * 50)
    print("ROS参数服务器读取器已启动")
    print("=" * 50)
    
    rate = rospy.Rate(1)  # 1Hz频率检查参数
    
    while not rospy.is_shutdown():
        try:
            # 从参数服务器获取参数
            nav_end = rospy.get_param('/nav_end', None)  # 获取导航终点，默认None
            operator = rospy.get_param('/operator', None)  # 获取运算符，默认None
            
            # 显示当前参数值
            print(f"当前参数值:")
            print(f"  NAV_END: {nav_end}")
            print(f"  OPERATOR: {operator}")
            
            # 检查参数是否都已设置
            if nav_end is not None and operator is not None:
                print(f"   完整参数获取成功！")
                print(f"   导航终点: {nav_end}")
                print(f"   运算符: {operator}")
                print("-" * 30)
            else:
                print("⏳ 等待参数设置...")
                print("-" * 30)
                
        except Exception as e:
            rospy.logerr(f"参数获取失败: {e}")
        
        rate.sleep()

if __name__ == '__main__':
    try:
        get_nav_and_operator()
    except rospy.ROSInterruptException:
        rospy.loginfo("参数读取器已停止")
