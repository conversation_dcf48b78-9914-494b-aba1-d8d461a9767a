#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
from std_msgs.msg import String, Int32

## 全局状态变量
NAV_END = None  # 存储检测到的导航终点编号
OPERATOR = None  # 存储检测到的运算符

## 语音关键词映射表（可扩展）
KEYWORDS = {
    "一": 11,  # 关键词到导航点编号的映射
    "二": 12,
    "三": 13
}

## 运算符映射表（可扩展）
OPERATORS = {
    "加":"+",  # 中文运算符到符号的映射
    "家":"+",
    "减":"-",
    "乘":"*",
    "除":"/"
}

# 发布者变量
arrive_pub = None
nav_end_pub = None
operator_pub = None


def chinese_callback(msg):
    """
    中文指令处理回调函数
    """
    global NAV_END, OPERATOR

    processed = False
    operator_found = False

    ## 关键词检测模块
    for keyword, value in KEYWORDS.items():
        if keyword in msg.data:
            NAV_END = value
            rospy.loginfo(f"关键词识别：{keyword}→导航点{NAV_END}")

            # 发布语音反馈
            feedback = f"终点为干{keyword}"
            arrive_pub.publish(feedback)

            # 发布导航终点
            try:
                nav_end_pub.publish(NAV_END)
            except Exception as e:
                rospy.logerr(f"导航点发布失败：{e}")

            processed = True
            break  # 跳出关键词循环

    ## 运算符检测模块
    for name, symbol in OPERATORS.items():
        if name in msg.data:
            OPERATOR = symbol
            rospy.loginfo(f"运算符识别: {name}→ {OPERATOR}")

            # 发布语音反馈
            feedback = f"运算符为{name}"
            arrive_pub.publish(feedback)

            # 发布运算符
            try:
                operator_pub.publish(OPERATOR)
            except Exception as e:
                rospy.logerr(f"运算符发布失败: {e}")

            operator_found = True
            break  # 跳出运算符循环

    ## 未识别处理
    if not processed and not operator_found:
        rospy.logwarn(f"未识别有效指令: {msg.data}")


def chinese_subscriber():
    """
    节点初始化函数
    功能:
    1. 初始化ROS节点
    2. 创建发布者/订阅者
    3. 启动消息循环
    """
    #arrive_pub 语音反馈通道 nav_end_pub导航目标通道 operator_pub运算符通道
    global arrive_pub, nav_end_pub, operator_pub

    # 节点初始化
    rospy.init_node('chinese_processor', anonymous=True)

    # 创建发布者
    arrive_pub = rospy.Publisher('/voiceWords', String, queue_size=10)  # 语音反馈
    nav_end_pub = rospy.Publisher('nav_end_topic', Int32, queue_size=10)  # 导航终点
    operator_pub = rospy.Publisher('operator_topic', String, queue_size=10)  # 运算符

    # 创建订阅者
    rospy.Subscriber("chinese_topic", String, chinese_callback)

    rospy.loginfo("中文指令处理器已启动")
    rospy.spin()  # 保持节点运行


if __name__ == '__main__':
    try:
        chinese_subscriber()
    except rospy.ROSInterruptException:
        rospy.loginfo("节点已终止")
        pass
