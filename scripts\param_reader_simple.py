#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy

def get_params_once():
    """
    一次性读取参数，不需要持续监控
    """
    rospy.init_node('param_reader_simple', anonymous=True)
    
    print("=" * 40)
    print("一次性参数读取器")
    print("=" * 40)
    
    try:
        # 读取参数
        nav_end = rospy.get_param('/nav_end', None)
        operator = rospy.get_param('/operator', None)
        
        print("当前参数值:")
        print(f"  NAV_END: {nav_end}")
        print(f"  OPERATOR: {operator}")
        
        if nav_end is not None and operator is not None:
            print("\n✅ 参数读取成功！")
            print(f"   导航终点: {nav_end}")
            print(f"   运算符: {operator}")
            return nav_end, operator
        else:
            print("\n❌ 参数未设置或为空")
            print("请确保 tts_pub_all.py 已经处理过语音输入")
            return None, None
            
    except Exception as e:
        print(f"❌ 参数读取失败: {e}")
        return None, None

if __name__ == '__main__':
    try:
        nav, op = get_params_once()
        print(f"\n程序结束，获取到: NAV={nav}, OP={op}")
    except rospy.ROSInterruptException:
        print("程序被中断")
