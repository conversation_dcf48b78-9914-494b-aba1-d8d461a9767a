#!/bin/bash

# 并行运行语音系统脚本
echo "=== 并行运行语音系统 ==="

# 设置路径 - 修正为正确的路径
BASE_PATH="/home/<USER>/abot_ws/src/robot_slam/scripts/voice_test/scripts"
cd "$BASE_PATH"

# 检查roscore
if ! pgrep -x "roscore" > /dev/null; then
    echo "启动roscore..."
    roscore &
    sleep 3
fi

echo "在后台并行启动所有程序..."

# 方法1: 使用后台进程并行启动
echo "启动 test_voice_all.py..."
python3 test_voice_all.py &
PID1=$!
sleep 2

echo "启动 test_0707.py..."
python3 test_0707.py &
PID2=$!
sleep 2

echo "启动 demo.py..."
python3 demo.py &
PID3=$!
sleep 2

echo "启动 fa.py..."
python3 fa.py &
PID4=$!

echo ""
echo "所有程序已启动！进程ID："
echo "test_voice_all.py: $PID1"
echo "test_0707.py: $PID2"
echo "demo.py: $PID3"
echo "fa.py: $PID4"
echo ""
echo "按 Ctrl+C 停止所有程序"

# 等待用户中断
trap 'echo "正在停止所有程序..."; kill $PID1 $PID2 $PID3 $PID4 2>/dev/null; exit' INT
wait

echo "所有程序已在独立终端中启动！"
echo "每个程序运行在单独的标签页中"
