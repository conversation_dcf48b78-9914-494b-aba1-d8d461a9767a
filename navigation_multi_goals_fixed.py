#!/usr/bin/env python2
# -*- coding: utf-8 -*-

import rospy
import actionlib
import os
from math import pi
from tf_conversions import transformations
from actionlib_msgs.msg import GoalStatus
from move_base_msgs.msg import MoveBaseAction, MoveBaseGoal
from nav_msgs.msg import Path
from geometry_msgs.msg import PoseWithCovarianceStamped, Twist, Point
from std_msgs.msg import String, Int32
from ar_track_alvar_msgs.msg import AlvarMarkers, AlvarMarker

# 全局状态变量
time = 0                   # 运动计时器
vision_id = None           # 视觉识别结果ID（来自摄像头）
find_id = None             # 定位结果ID（来自激光雷达）
ar_id = None               # AR标记识别结果ID（来自AR码）
vlm_id = None              # 视觉语言模型识别结果ID
nav_end = None             # 导航终点ID（来自语音指令）
operator = None            # 数学运算符（来自语音指令）
image_recognition_result1 = None  # 第一阶段图像识别结果
image_recognition_result2 = None  # 第二阶段图像识别结果
calculation_result = None  # 最终计算结果
ar_id_navi = None          # AR导航标志位（0-关闭 1-开启）

class navigation_demo:
    def __init__(self):
        """系统初始化：建立ROS通信连接与参数配置"""
        # 初始化姿态布局（用于重定位）
        self.set_pose_pub = rospy.Publisher('/initialpose', PoseWithCovarianceStamped, queue_size=5)
        
        # 语言反馈发布器（用于文本转语音）
        self.arrive_pub = rospy.Publisher('/voiceWords', String, queue_size=10)
        
        # 导航终点订阅（来自语音识别节点）
        self.nav_end_sub = rospy.Subscriber('nav_end_topic', Int32, self.nav_end_callback)
        
        # VLM视觉识别订阅（来自视觉语言模型节点）
        self.vlm_id_sub = rospy.Subscriber('/vlm_id_topic', Int32, self.vlm_id_callback)
        
        # 物体位置订阅（来自模板匹配处理节点）
        self.find_sub = rospy.Subscriber('/object_position', Point, self.find_cb)
        
        # AR标记订阅（来自AR识别节点）
        self.ar_sub = rospy.Subscriber('/ar_pose_marker', AlvarMarkers, self.ar_cb)
        
        # 运算符订阅（来自语音识别节点）
        self.operator_sub = rospy.Subscriber('operator_topic', String, self.operator_callback)
        
        # 导航动作客户端（连接move_base节点）
        self.move_base = actionlib.SimpleActionClient("move_base", MoveBaseAction)
        if self.move_base.wait_for_server(rospy.Duration(60)):
            rospy.loginfo("Connected to move_base server")
        else:
            rospy.logerr("Timed out waiting for move_base server")
    
    def set_pose(self, p):
        """设置初始位姿（重定位）
        Args:
            p: 位姿三元组 (x, y, theta)
        Returns:
            bool: 是否设置成功
        """
        if self.set_pose_pub is None:
            rospy.logerr("Initial pose publisher not initialized")
            return False
        
        x, y, th = p  # 解包坐标
        
        # 构建位姿消息
        pose = PoseWithCovarianceStamped()
        pose.header.stamp = rospy.Time.now()
        pose.header.frame_id = 'map'
        pose.pose.pose.position.x = x
        pose.pose.pose.position.y = y
        pose.pose.pose.position.z = 0.0  # 2D导航中z通常为0
        
        # 欧拉角转四元数
        q = transformations.quaternion_from_euler(0.0, 0.0, th/180.0*pi)
        pose.pose.pose.orientation.x = q[0]
        pose.pose.pose.orientation.y = q[1]
        pose.pose.pose.orientation.z = q[2]
        pose.pose.pose.orientation.w = q[3]
        
        # 发布位姿
        self.set_pose_pub.publish(pose)
        rospy.loginfo("设置初始位姿: x=%.2f, y=%.2f, theta=%.1f度", x, y, th)
        return True
    
    def __done_cb(self, status, result):
        """导航完成回调：处理最终状态"""
        rospy.loginfo("导航完成！状态码:%d 结果:%s", status, result)
    
    def __active_cb(self):
        """导航激活回调：任务开始通知"""
        rospy.loginfo("[导航系统] 路径规划已激活")
    
    def __feedback_cb(self, feedback):
        """导航反馈回调：实时状态更新"""
        pass  # 可根据需要添加反馈处理
    
    def goto(self, p):
        """执行导航到指定点
        Args:
            p: 目标点位姿 (x, y, theta)
        Returns:
            bool: 是否到达目标
        """
        rospy.loginfo("[导航系统] 前往目标点 %s", p)
        
        # 构建导航目标
        goal = MoveBaseGoal()
        goal.target_pose.header.frame_id = 'map'
        goal.target_pose.header.stamp = rospy.Time.now()
        goal.target_pose.pose.position.x = p[0]
        goal.target_pose.pose.position.y = p[1]
        goal.target_pose.pose.position.z = 0.0
        
        # 角度转换
        q = transformations.quaternion_from_euler(0.0, 0.0, p[2]/180.0*pi)
        goal.target_pose.pose.orientation.x = q[0]
        goal.target_pose.pose.orientation.y = q[1]
        goal.target_pose.pose.orientation.z = q[2]
        goal.target_pose.pose.orientation.w = q[3]
        
        # 发送目标并等待结果
        self.move_base.send_goal(goal, 
                                done_cb=self.__done_cb,
                                active_cb=self.__active_cb,
                                feedback_cb=self.__feedback_cb)
        
        result = self.move_base.wait_for_result(rospy.Duration(60))
        
        # 超时处理
        if not result:
            self.move_base.cancel_goal()
            rospy.logwarn("导航超时")
            return False
        else:
            state = self.move_base.get_state()
            if state == GoalStatus.SUCCEEDED:
                rospy.loginfo("成功到达目标点 %s", p)
                return True
            return False
    
    def end(self):
        """结束导航任务"""
        self.move_base.cancel_all_goals()
        rospy.loginfo("导航任务结束")
    
    def trigger_photo_and_upload(self):
        """触发拍照流程：通过参数服务器与其他节点交互"""
        rospy.set_param('/top_view_shot_node/im_flag', 1)  # 设置拍照标志
        rospy.sleep(2)  # 等待拍照完成
        result = rospy.get_param('/top_view_shot_node/result', None)  # 获取拍照结果
        rospy.set_param('/top_view_shot_node/im_flag', 255)  # 重置标志位
        return result
    
    def vlm_id_callback(self, msg):
        """VLM识别结果回调：处理视觉语言模型的输出"""
        global vlm_id, vision_id
        vlm_id = msg.data
        rospy.loginfo("收到 vlm_id: %d", vlm_id)
        
        # 有效性检查 (ID范围1-8)
        if 1 <= vlm_id <= 8:
            vision_id = vlm_id  # 更新视觉识别结果
        else:
            rospy.logwarn("无效的vlm_id: %d", vlm_id)
    
    def find_cb(self, data):
        """物体位置回调：处理模板匹配结果"""
        global find_id
        interval_mapping = {
            (10, 19): 1,
            (20, 29): 2,
        }
        point_msg = data
        find_id = None
        
        # 遍历区间映射表
        for (start, end), id_value in interval_mapping.items():
            if start <= point_msg.z <= end:
                find_id = id_value
                rospy.loginfo("收到find_id: %d", find_id)
                break
    
    def ar_cb(self, data):
        """AR标记回调：处理AR识别结果"""
        global ar_id, ar_id_navi
        for marker in data.markers:
            if ar_id_navi == 1:  # 仅在AR导航模式下处理
                ar_id = marker.id
                rospy.loginfo("收到ar_id: %d", ar_id)
    
    def nav_end_callback(self, msg):
        """导航终点回调：更新目标点ID"""
        global nav_end
        nav_end = msg.data
        rospy.loginfo("收到导航终点ID: %d", nav_end)
    
    def operator_callback(self, msg):
        """运算符回调：处理数学运算符"""
        global operator
        operator = msg.data
        rospy.loginfo("收到运算符: %s", operator)

def navigate_and_calculate(navi, goals, index1, index2):
    """导航和计算函数"""
    global image_recognition_result1, image_recognition_result2, calculation_result
    global vision_id, ar_id, find_id, ar_id_navi, operator, nav_end

    # === 第一阶段导航与识别 ===
    navi.goto(goals[index1])  # 前往第一个目标点
    ar_id_navi = 1  # 开启AR识别模式
    navi.trigger_photo_and_upload()  # 执行拍照
    rospy.sleep(5)  # 等待识别结果

    # 多源数据融合策略（优先级：激光 > 视觉 > AR）
    image_recognition_result1 = find_id or vision_id or ar_id
    if not image_recognition_result1:
        rospy.logerr("第一阶段识别失败")
        # 模拟识别结果用于测试
        image_recognition_result1 = 2
        rospy.logwarn("使用模拟识别结果: %d", image_recognition_result1)

    ar_id_navi = 0  # 关闭AR识别
    # === 第二阶段导航与识别 ===
    navi.goto(goals[index2])  # 前往第二个目标点
    ar_id_navi = 1  # 重新开启AR识别
    navi.trigger_photo_and_upload()
    rospy.sleep(5)

    image_recognition_result2 = find_id or vision_id or ar_id
    if not image_recognition_result2:
        rospy.logerr("第二阶段识别失败")
        # 模拟识别结果用于测试
        image_recognition_result2 = 3
        rospy.logwarn("使用模拟识别结果: %d", image_recognition_result2)

    ar_id_navi = 0  # 最终关闭AR识别

    # === 计算处理 ===
    try:
        if operator == '+':
            calculation_result = abs(image_recognition_result1 + image_recognition_result2)
        elif operator == '-':
            calculation_result = abs(image_recognition_result1 - image_recognition_result2)
        elif operator == '*':
            calculation_result = abs(image_recognition_result1 * image_recognition_result2)
        elif operator == '/':
            calculation_result = abs(image_recognition_result1 / image_recognition_result2)
        else:
            raise ValueError("无效运算符或操作数")
    except Exception as e:
        rospy.logerr("计算错误: %s", str(e))
        return

    # 设置反馈信息
    operator_chinese = {"+":"加", "-":"减", "*":"乘", "/":"除"}.get(operator, "未知")
    feedback = f"计算结果: {image_recognition_result1} {operator_chinese} {image_recognition_result2} 等于 {calculation_result}"
    navi.arrive_pub.publish(feedback)

    # === 结果导航 ===
    if 0 <= calculation_result < len(goals):
        navi.goto(goals[int(calculation_result)])  # 导航到计算结果对应的目标点
        navi.end()  # 执行结束动作
        rospy.sleep(3)
        # 重置识别结果
        vision_id = find_id = ar_id = None
        # 设置最终导航点
        nav_end = int(calculation_result)
    else:
        rospy.logerr("计算结果超出目标点范围")

if __name__ == "__main__":
    # ROS节点初始化
    rospy.init_node('navigation_demo', anonymous=True)

    # 参数获取与处理
    goallistX = rospy.get_param('~goallistX', '2.0, 2.0').replace(' ', ',')  # 中文逗号替换
    goallistY = rospy.get_param('~goallistY', '2.0, 4.0').replace(' ', ',')
    goallistYaw = rospy.get_param('~goallistYaw', '0, 90.0').replace(' ', ',')

    # 目标点动态生成
    goals = [
        [float(x), float(y), float(yaw)]
        for x, y, yaw in zip(
            goallistX.split(','),
            goallistY.split(','),
            goallistYaw.split(',')
        )
    ]

    print('输入启动系统: ')
    user_input = input()

    if user_input == '1':
        # 初始化导航系统
        navi = navigation_demo()

        # 发布音频触发信号
        audio_pub = rospy.Publisher('audio_topic', String, queue_size=10)
        for _ in range(2):
            audio_pub.publish("audio trigger")
            rospy.sleep(1)

        # 主任务执行
        try:
            # 区域导航任务
            navigate_and_calculate(navi, goals, 0, 10)  # 区域1
            navigate_and_calculate(navi, goals, 14, 15) # 区域2
            navigate_and_calculate(navi, goals, 16, 17) # 区域3

            # 最终导航
            if nav_end and 0 <= nav_end < len(goals):
                navi.goto(goals[nav_end])
                navi.end()
                navi.arrive_pub.publish(f"到达{nav_end}号终点，任务完成")
            else:
                rospy.logerr("无效终点ID")

        except Exception as e:
            rospy.logerr("主流程异常: %s", str(e))

