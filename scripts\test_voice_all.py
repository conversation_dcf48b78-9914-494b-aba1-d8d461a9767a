#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
from std_msgs.msg import String, Int32

# 全局变量，外部可以直接访问
voice_feedback = []  # 语音反馈列表，存储多条消息
nav_target = None  # 导航目标点
operator = None  # 运算符


# 外部回调函数，外部可以重新定义
def voice_callback(msg):
    """语音反馈回调，外部可以重新定义此函数"""
    global voice_feedback
    voice_feedback.append(msg.data)  # 累积语音反馈
    rospy.loginfo("收到语音反馈: %s" % msg.data)
    rospy.loginfo("当前语音反馈列表: %s" % voice_feedback)
    check_complete_data()


def nav_callback(msg):
    """导航目标回调，外部可以重新定义此函数"""
    global nav_target
    nav_target = msg.data
    rospy.loginfo("收到导航目标: %d" % msg.data)
    check_complete_data()


def operator_callback(msg):
    """运算符回调，外部可以重新定义此函数"""
    global operator
    operator = msg.data
    rospy.loginfo("收到运算符: %s" % msg.data)
    check_complete_data()


def on_complete_data(voice_list, nav, op):
    """完整数据处理，外部可以重新定义此函数"""
    print(" 完整数据接收完毕！")
    print("1. 语音反馈列表: %s" % voice_list)
    print("2. 导航到点: %d" % nav)
    print("3. 执行运算: %s" % op)


def check_complete_data():
    """检查是否接收到完整数据"""
    # 检查是否有语音反馈、导航目标和运算符
    if len(voice_feedback) >= 2 and nav_target is not None and operator is not None:
        on_complete_data(voice_feedback, nav_target, operator)
        reset_all_data()


def reset_all_data():
    """重置所有数据"""
    global voice_feedback, nav_target, operator
    voice_feedback = []  # 重置为空列表
    nav_target = None
    operator = None
    print("状态已重置")


class MessageReceiver:
    """接收test_0707.py发布的所有消息"""

    def __init__(self):
        # 初始化节点
        rospy.init_node('message_receiver', anonymous=True)

        # 创建订阅者，直接使用外部回调函数
        self.voice_sub = rospy.Subscriber('/voiceWords', String, voice_callback)
        self.nav_sub = rospy.Subscriber('nav_end_topic', Int32, nav_callback)
        self.operator_sub = rospy.Subscriber('operator_topic', String, operator_callback)

        rospy.loginfo("消息接收器已启动，等待消息...")
        print("=" * 50)
        print("消息接收器已启动")
        print("监听以下topic:")
        print("- /voiceWords (语音反馈)")
        print("- nav_end_topic (导航目标)")
        print("- operator_topic (运算符)")
        print("=" * 50)

    def run(self):
        """运行消息接收器"""
        rospy.spin()


if __name__ == '__main__':
    try:
        receiver = MessageReceiver()
        receiver.run()  # 使用类中的run方法
    except rospy.ROSInterruptException:
        rospy.loginfo("消息接收器已停止")
