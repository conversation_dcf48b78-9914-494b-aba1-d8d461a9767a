#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy

def get_params_simple():
    """
    简单的一次性接收tts_pub_all.py中的NAV_END和OPERATOR参数
    """
    rospy.init_node('param_getter', anonymous=True)

    print("=" * 40)
    print("获取tts_pub_all.py的参数")
    print("=" * 40)

    try:
        # 从参数服务器获取参数
        nav_end = rospy.get_param('/nav_end', None)
        operator = rospy.get_param('/operator', None)
        # 蒋兴宇编写代码的地方

    except Exception as e:
        print(f"❌ 参数获取失败: {e}")

if __name__ == '__main__':
    try:
        get_params_simple()
    except rospy.ROSInterruptException:
        print("程序被中断")
